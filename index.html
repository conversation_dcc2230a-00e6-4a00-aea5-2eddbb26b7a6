<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Coalition Member Splash Pages - Final Design</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;700;900&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #0A0A0A;
            color: #E0E0E0;
            overflow-x: hidden;
        }

        .page-wrapper {
            width: 100%;
            transition: opacity 0.7s ease-in-out;
        }

        .page { display: none; }
        .page.active { display: block; }

        .background-image-container {
            position: fixed; top: 0; left: 0;
            width: 100vw; height: 100vh;
            z-index: -2;
        }

        .background-image {
            width: 100%; height: 100%;
            object-fit: cover; object-position: center;
            opacity: 0.35;
            filter: grayscale(100%);
        }
        
        .background-overlay {
            position: fixed; top: 0; left: 0;
            width: 100vw; height: 100vh;
            z-index: -1;
            background: radial-gradient(circle, rgba(10,10,10,0.1) 0%, rgba(10,10,10,1) 85%);
        }

        .grid-lines {
            position: fixed; top: 0; left: 0;
            width: 100vw; height: 100vh;
            pointer-events: none; z-index: 0;
        }
        .grid-lines .line {
            position: absolute;
            background-color: rgba(255, 255, 255, 0.05);
        }
        .grid-lines .line.vertical { width: 1px; height: 100%; }
        .grid-lines .line.horizontal { height: 1px; width: 100%; }
        
        /* FINAL HERO - VERTICALLY STACKED TO PREVENT OVERLAP */
        .hero-section {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            min-height: 100vh;
            width: 100%;
            padding: 2rem;
            box-sizing: border-box;
            position: relative;
            overflow: hidden;
        }

        header {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            z-index: 20;
        }
        
        .hero-content-stack {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            gap: 2rem; /* Spacing between bio, name, and image container */
            flex-grow: 1; /* Allows this to take up available space */
            padding: 2rem 0;
        }

        .hero-summary-container {
            display: flex;
            gap: 2rem;
            width: 100%;
            max-width: 800px;
            justify-content: center;
        }

        .hero-summary-col {
            flex: 1;
            color: #D1D5DB;
            font-size: clamp(0.75rem, 1vw, 0.875rem);
            line-height: 1.6;
            text-align: left;
        }
        
        .person-name-container {
            width: 100%;
        }

        .person-name {
            font-size: clamp(4rem, 14vw, 12rem);
            font-weight: 900;
            line-height: 1;
            color: #FFFFFF;
        }
        
        .small-portrait-container {
            width: 100%;
            max-width: 800px;
            display: flex;
            justify-content: flex-end; /* Aligns image to the right */
        }

        .small-portrait {
            width: 200px;
            height: 200px;
        }

        .small-portrait img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            filter: grayscale(100%);
        }
        
        .page-number {
            position: absolute;
            right: 2rem;
            top: 50%;
            transform: translateY(-50%);
            font-family: monospace;
            font-size: 2rem;
            font-weight: 700;
            color: rgba(255,255,255,0.8);
            z-index: 10;
        }

        .footer-nav {
            width: 100%;
            display: flex;
            justify-content: flex-start; /* Aligns to left */
            gap: 0.5rem;
            z-index: 20;
        }
        .footer-nav button {
            width: 3.5rem; height: 3.5rem;
            border-radius: 9999px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s;
            color: #FFFFFF;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .footer-nav button.prev { background-color: transparent; }
        .footer-nav button.next { background-color: #facc15; color: #000; border: none;}
        .footer-nav button:hover { transform: scale(1.1); }
        
        .content-container {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 4rem 2rem;
        }
        .content-section {
            padding: 4rem 0;
            border-top: 1px solid rgba(255,255,255,0.1);
            display: grid;
            grid-template-columns: repeat(12, 1fr);
            gap: 1.5rem;
        }
        .content-section h2 {
            grid-column: 1 / span 3;
            font-size: 1.5rem;
            font-weight: 700;
            color: #FFFFFF;
        }
        .content-section .text-block {
            grid-column: 4 / span 8;
            font-size: 1rem;
            line-height: 1.8;
            color: #A0A0A0;
        }
        .links-section a {
            font-size: 1.25rem; color: #E0E0E0; text-decoration: none;
            transition: color 0.3s; display: block; padding: 0.5rem 0;
        }
        .links-section a:hover { color: #facc15; }

        @media (max-width: 768px) {
            .hero-summary-container, .small-portrait-container { display: none; }
        }

    </style>
</head>
<body>
    <div class="page-wrapper">

        <!-- Page 1: Elon Musk -->
        <div id="page-1" class="page active">
            <div class="background-image-container">
                <img src="https://www.motherjones.com/wp-content/uploads/2024/08/20240822_elon-subpoena_2000.png?w=990" 
                     onerror="this.onerror=null;this.src='https://placehold.co/1920x1080/000000/FFFFFF?text=Image+Not+Found';"
                     class="background-image" alt="Elon Musk Background">
            </div>
            <div class="background-overlay"></div>
            <div class="grid-lines">
                <div class="line vertical" style="left: 33.33%;"></div>
                <div class="line vertical" style="left: 66.66%;"></div>
                <div class="line horizontal" style="top: 50%;"></div>
            </div>

            <main>
                <section class="hero-section">
                    <header>
                        <div class="font-bold text-2xl tracking-wider" style="color: #facc15;">H-LEGENDS</div>
                        <div class="font-mono text-2xl hidden md:block">...</div>
                    </header>
                    
                    <div class="hero-content-stack">
                        <div class="hero-summary-container">
                            <div class="hero-summary-col">
                                <p>An entrepreneur who co-founded PayPal and has since focused on revolutionizing terrestrial and space travel.</p>
                            </div>
                            <div class="hero-summary-col">
                                <p>His ventures include Tesla, Inc., the electric vehicle pioneer, and SpaceX, the private aerospace manufacturer.</p>
                            </div>
                        </div>
                        <div class="person-name-container">
                            <h1 class="person-name">Elon Musk</h1>
                        </div>
                         <div class="small-portrait-container">
                            <div class="small-portrait">
                                 <img src="https://images.squarespace-cdn.com/content/v1/5794fb4937c58136e656c216/1615485910995-LV32LHWLPAI5DJVCXKO7/Elon+Musk" 
                                      onerror="this.onerror=null;this.src='https://placehold.co/250x250/000000/FFFFFF?text=img';"
                                      alt="Portrait of Elon Musk">
                            </div>
                        </div>
                    </div>

                    <div class="page-number">001</div>
                    <nav class="footer-nav">
                        <button class="prev" onclick="navigate(-1)">←</button>
                        <button class="next" onclick="navigate(1)">→</button>
                    </nav>
                </section>
                
                <div class="content-container">
                    <section class="content-section">
                        <h2>Biography</h2>
                        <div class="text-block">
                            <p>Elon Reeve Musk is a business magnate and investor renowned for his transformative impact on technology and transportation. Born in South Africa, he showed an early aptitude for computers and entrepreneurship. After moving to Canada and later the United States, he co-founded Zip2, a web software company, which was acquired by Compaq for $307 million.</p>
                        </div>
                    </section>
                    <section class="content-section links-section">
                        <h2>Connect</h2>
                        <div class="text-block">
                             <a href="#" target="_blank">Official Website: X.com</a>
                             <a href="#" target="_blank">Official Website: Tesla</a>
                             <a href="#" target="_blank">Official Website: SpaceX</a>
                        </div>
                    </section>
                </div>
            </main>
        </div>

        <!-- Page 2: Donald Trump -->
        <div id="page-2" class="page">
             <div class="background-image-container">
                <img src="https://static01.nyt.com/images/2015/10/04/magazine/04trump1/04trump1_opener-superJumbo.jpg" 
                     onerror="this.onerror=null;this.src='https://placehold.co/1920x1080/000000/FFFFFF?text=Image+Not+Found';"
                     class="background-image" alt="Donald Trump Background">
            </div>
            <div class="background-overlay"></div>
            <div class="grid-lines">
                <div class="line vertical" style="left: 25%;"></div>
                <div class="line vertical" style="left: 75%;"></div>
                <div class="line horizontal" style="top: 40%;"></div>
            </div>

            <main>
                <section class="hero-section">
                    <header>
                        <div class="font-bold text-2xl tracking-wider" style="color: #facc15;">H-LEGENDS</div>
                        <div class="font-mono text-2xl hidden md:block">...</div>
                    </header>
                    <div class="hero-content-stack">
                        <div class="hero-summary-container">
                            <div class="hero-summary-col">
                                <p>A real estate developer and media personality who built a global brand licensing his name.</p>
                            </div>
                             <div class="hero-summary-col">
                                <p>Leveraging his public profile, he transitioned into politics, eventually becoming the 45th President of the United States.</p>
                            </div>
                        </div>
                        <div class="person-name-container">
                            <h1 class="person-name">Donald Trump</h1>
                        </div>
                         <div class="small-portrait-container">
                            <div class="small-portrait">
                                 <img src="https://media.newyorker.com/photos/641ca6e1428b96a33ea5bfbb/1:1/w_1004,h_1004,c_limit/Glasser-Trump-Troll.jpg" 
                                      onerror="this.onerror=null;this.src='https://placehold.co/250x250/000000/FFFFFF?text=img';"
                                      alt="Portrait of Donald Trump">
                            </div>
                        </div>
                    </div>
                    <div class="page-number">002</div>
                    <nav class="footer-nav">
                        <button class="prev" onclick="navigate(-1)">←</button>
                        <button class="next" onclick="navigate(1)">→</button>
                    </nav>
                </section>
                
                <div class="content-container">
                    <section class="content-section">
                        <h2>Career Highlights</h2>
                        <div class="text-block">
                            <p><strong>Real Estate Empire:</strong> Trump's signature achievement is his development of numerous iconic properties, particularly in New York City. Trump Tower on Fifth Avenue became a symbol of 1980s luxury and excess. He expanded his brand globally, licensing the Trump name for various real estate projects.</p>
                        </div>
                    </section>
                    <section class="content-section links-section">
                        <h2>Connect</h2>
                        <div class="text-block">
                             <a href="#" target="_blank">Official Website</a>
                             <a href="#" target="_blank">Truth Social Profile</a>
                        </div>
                    </section>
                </div>
            </main>
        </div>

        <!-- Page 3: Jeff Bezos -->
        <div id="page-3" class="page">
            <div class="background-image-container">
                <img src="https://wallpapers.com/images/hd/black-and-white-jeff-bezos-6g7c5wl1z83x5gi0.jpg" 
                     onerror="this.onerror=null;this.src='https://placehold.co/1920x1080/000000/FFFFFF?text=Image+Not+Found';"
                     class="background-image" alt="Jeff Bezos Background">
            </div>
            <div class="background-overlay"></div>
            <div class="grid-lines">
                 <div class="line vertical" style="left: 50%;"></div>
                 <div class="line horizontal" style="top: 66.66%;"></div>
            </div>

             <main>
                <section class="hero-section">
                    <header>
                        <div class="font-bold text-2xl tracking-wider" style="color: #facc15;">H-LEGENDS</div>
                        <div class="font-mono text-2xl hidden md:block">...</div>
                    </header>
                    <div class="hero-content-stack">
                        <div class="hero-summary-container">
                            <div class="hero-summary-col">
                                <p>Pioneered the e-commerce landscape by founding Amazon in his garage as an online bookstore.</p>
                            </div>
                            <div class="hero-summary-col">
                                <p>He expanded the company into the world's largest online retailer and a dominant force in cloud computing with AWS.</p>
                            </div>
                        </div>
                        <div class="person-name-container">
                            <h1 class="person-name">Jeff Bezos</h1>
                        </div>
                        <div class="small-portrait-container">
                            <div class="small-portrait">
                                 <img src="https://assets.gqindia.com/photos/5cdc38c578abdc4c795c1622/master/w_1600%2Cc_limit/gq-jeff-bezos-laughing-720.jpg" 
                                      onerror="this.onerror=null;this.src='https://placehold.co/250x250/000000/FFFFFF?text=img';"
                                      alt="Portrait of Jeff Bezos">
                            </div>
                        </div>
                    </div>
                    <div class="page-number">003</div>
                    <nav class="footer-nav">
                        <button class="prev" onclick="navigate(-1)">←</button>
                        <button class="next" onclick="navigate(1)">→</button>
                    </nav>
                </section>
                
                <div class="content-container">
                     <section class="content-section">
                        <h2>Major Ventures</h2>
                        <div class="text-block">
                             <p><strong>Amazon:</strong> From its origins as an online bookstore, Bezos guided Amazon's diversification into a vast array of product categories and services, including electronics, streaming media, and most notably, cloud computing with Amazon Web Services (AWS).</p>
                        </div>
                    </section>
                    <section class="content-section links-section">
                        <h2>Connect</h2>
                        <div class="text-block">
                             <a href="#" target="_blank">Blue Origin Website</a>
                             <a href="#" target="_blank">The Washington Post</a>
                             <a href="#" target="_blank">Bezos Earth Fund</a>
                        </div>
                    </section>
                </div>
            </main>
        </div>
    </div>

    <script>
        let currentPage = 1;
        const totalPages = 3;
        const pageWrapper = document.querySelector('.page-wrapper');

        function showPage(pageNumber) {
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('active');
            });
            const targetPage = document.getElementById(`page-${pageNumber}`);
            if (targetPage) {
                targetPage.classList.add('active');
                window.scrollTo({ top: 0, behavior: 'instant' });
            }
        }

        function navigate(direction) {
            pageWrapper.style.opacity = 0;
            setTimeout(() => {
                let newPage = currentPage + direction;
                if (newPage > totalPages) newPage = 1;
                if (newPage < 1) newPage = totalPages;
                currentPage = newPage;
                showPage(currentPage);
                pageWrapper.style.opacity = 1;
            }, 500);
        }

        document.addEventListener('DOMContentLoaded', () => {
            showPage(currentPage);
        });
    </script>
</body>
</html>
